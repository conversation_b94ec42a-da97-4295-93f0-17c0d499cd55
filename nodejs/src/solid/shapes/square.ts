import Shape from "./base/shape";

export default class Square extends Shape {
  private length: number;

  constructor(length: number) {
    super();
    this.length = length;
  }

  getLength(): number {
    return this.length;
  }

  area(): number {
    return this.getLength() * this.getLength();
  }

  getType(): string {
    return "Square";
  }

  getProperties(): Record<string, any> {
    return {
      length: this.length,
    };
  }
}
