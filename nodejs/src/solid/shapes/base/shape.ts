// Interface for shapes that can calculate area
export interface IShape {
  area(): number;
}

// Interface for shapes that can be formatted
export interface IFormattable {
  getType(): string;
  getProperties(): Record<string, any>;
}

// Abstract base class implementing the interfaces
export default abstract class Shape implements IShape, IFormattable {
  abstract area(): number;
  abstract getType(): string;
  abstract getProperties(): Record<string, any>;
}
