import Shape from "./base/shape";

export default class Circle extends Shape {
  private radius: number;

  constructor(radius: number) {
    super();
    this.radius = radius;
  }

  getRadius(): number {
    return this.radius;
  }

  area(): number {
    return Math.PI * this.getRadius() * this.getRadius();
  }

  getType(): string {
    return "Circle";
  }

  getProperties(): Record<string, any> {
    return {
      radius: this.radius,
    };
  }
}
