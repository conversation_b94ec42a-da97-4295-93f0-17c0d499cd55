import Shape3D from "./base/shape3d";

export default class Cube extends Shape3D {
  private length: number;

  constructor(length: number) {
    super();
    this.length = length;
  }

  getLength(): number {
    return this.length;
  }

  area(): number {
    return 6 * this.getLength() * this.getLength(); // Surface area of cube
  }

  volume(): number {
    return this.getLength() * this.getLength() * this.getLength();
  }

  getType(): string {
    return "Cube";
  }

  getProperties(): Record<string, any> {
    return {
      length: this.length,
    };
  }
}
