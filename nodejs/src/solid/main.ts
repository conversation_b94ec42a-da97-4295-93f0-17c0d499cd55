import AreaCalculator from "./areaCalcualtor";
import Circle from "./shapes/circle";
import Formatter from "./formater";
import Square from "./shapes/square";
import Cube from "./shapes/cube";
import Shape from "./shapes/base/shape";

const areaCalculator = new AreaCalculator();
const circle = new Circle(10);
const square = new Square(10);
const cube = new Cube(5);
const shapes: Shape[] = [circle, square, cube];
const formatter = new Formatter();

console.log("CSV Output:");
console.log(formatter.toCsv(shapes));

console.log("\nJSON Output:");
console.log(formatter.toJson(shapes));

console.log("\nTotal Area:");
const sum = areaCalculator.sum(shapes);
console.log(sum);
