import { IFormattable } from "./shapes/base/shape";

// Interface for output formatters (Interface Segregation Principle)
export interface IOutputFormatter {
  format(shapes: IFormattable[]): string;
}

// JSON formatter implementation
export class JsonFormatter implements IOutputFormatter {
  format(shapes: IFormattable[]): string {
    return JSON.stringify(
      shapes.map((shape) => ({
        type: shape.getType(),
        properties: shape.getProperties(),
      }))
    );
  }
}

// CSV formatter implementation
export class CsvFormatter implements IOutputFormatter {
  format(shapes: IFormattable[]): string {
    return shapes
      .map((shape) => {
        const props = shape.getProperties();
        const propValues = Object.values(props).join(",");
        return `${shape.getType()},${propValues}`;
      })
      .join("\n");
  }
}

// Main formatter class that depends on abstractions (Dependency Inversion Principle)
export default class Formatter {
  private jsonFormatter: IOutputFormatter;
  private csvFormatter: IOutputFormatter;

  constructor(
    jsonFormatter: IOutputFormatter = new JsonFormatter(),
    csvFormatter: IOutputFormatter = new CsvFormatter()
  ) {
    this.jsonFormatter = jsonFormatter;
    this.csvFormatter = csvFormatter;
  }

  toJson(shapes: IFormattable[]): string {
    return this.jsonFormatter.format(shapes);
  }

  toCsv(shapes: IFormattable[]): string {
    return this.csvFormatter.format(shapes);
  }
}
